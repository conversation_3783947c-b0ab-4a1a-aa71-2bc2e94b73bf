﻿using Core.DataAccess.EntityFramework;
using Core.Entities.Concrete;
using Core.Utilities.Results;
using DataAccess.Abstract;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DataAccess.Concrete.EntityFramework
{
    public class EfUserDeviceDal : EfEntityRepositoryBase<UserDevice, GymContext>, IUserDeviceDal
    {
        // Constructor injection (Scalability için)
        public EfUserDeviceDal(GymContext context) : base(context)
        {
        }

        // Backward compatibility constructor
        public EfUserDeviceDal() : base()
        {
        }

        public List<UserDevice> GetActiveDevicesByUserId(int userId)
        {
            return _context.UserDevices
                .Where(d => d.UserId == userId && d.IsActive)
                .ToList();
        }

        public UserDevice GetByRefreshToken(string refreshToken)
        {
            return _context.UserDevices
                .FirstOrDefault(d => d.RefreshToken == refreshToken && d.IsActive);
        }

        public IResult AddDeviceWithManagement(UserDevice device)
        {
            using (var context = new GymContext())
            {
                const int MAX_ACTIVE_DEVICES = 5; // Web için
                const int MAX_MOBILE_DEVICES = 1; // Mobil için sadece 1 cihaz

                CleanExpiredTokens();

                var activeDevices = context.UserDevices
                    .Where(d => d.UserId == device.UserId && d.IsActive)
                    .ToList();

                // Mobil cihaz kontrolü (DeviceInfo'da "Mobile" geçiyorsa mobil)
                bool isMobileDevice = device.DeviceInfo?.Contains("Mobile", StringComparison.OrdinalIgnoreCase) == true ||
                                    device.DeviceInfo?.Contains("Android", StringComparison.OrdinalIgnoreCase) == true ||
                                    device.DeviceInfo?.Contains("iOS", StringComparison.OrdinalIgnoreCase) == true ||
                                    device.DeviceInfo?.Contains("Flutter", StringComparison.OrdinalIgnoreCase) == true;

                if (isMobileDevice)
                {
                    // Mobil için: Tüm eski mobil cihazları revoke et (sadece 1 mobil cihaz)
                    var existingMobileDevices = activeDevices.Where(d =>
                        d.DeviceInfo?.Contains("Mobile", StringComparison.OrdinalIgnoreCase) == true ||
                        d.DeviceInfo?.Contains("Android", StringComparison.OrdinalIgnoreCase) == true ||
                        d.DeviceInfo?.Contains("iOS", StringComparison.OrdinalIgnoreCase) == true ||
                        d.DeviceInfo?.Contains("Flutter", StringComparison.OrdinalIgnoreCase) == true).ToList();

                    foreach (var existingDevice in existingMobileDevices)
                    {
                        existingDevice.IsActive = false;
                        existingDevice.RefreshToken = null;
                        existingDevice.RefreshTokenExpiration = null;
                        context.UserDevices.Update(existingDevice);
                    }
                }
                else
                {
                    // Web için: Maksimum cihaz sayısı kontrolü
                    var webDevices = activeDevices.Where(d =>
                        !(d.DeviceInfo?.Contains("Mobile", StringComparison.OrdinalIgnoreCase) == true ||
                          d.DeviceInfo?.Contains("Android", StringComparison.OrdinalIgnoreCase) == true ||
                          d.DeviceInfo?.Contains("iOS", StringComparison.OrdinalIgnoreCase) == true ||
                          d.DeviceInfo?.Contains("Flutter", StringComparison.OrdinalIgnoreCase) == true)).ToList();

                    if (webDevices.Count >= MAX_ACTIVE_DEVICES)
                    {
                        var oldestDevice = webDevices
                            .OrderBy(d => d.CreatedAt)
                            .First();
                        oldestDevice.IsActive = false;
                        oldestDevice.RefreshToken = null;
                        oldestDevice.RefreshTokenExpiration = null;
                        context.UserDevices.Update(oldestDevice);
                    }
                }

                context.UserDevices.Add(device);
                context.SaveChanges();
                return new SuccessResult();
            }
        }

        public void CleanExpiredTokens()
        {
            using (var context = new GymContext())
            {
                var expiredDevices = context.UserDevices.Where(d =>
                    d.IsActive &&
                    d.RefreshTokenExpiration.HasValue &&
                    d.RefreshTokenExpiration.Value < DateTime.Now).ToList();

                foreach (var device in expiredDevices)
                {
                    device.IsActive = false;
                    device.RefreshToken = null;
                    device.RefreshTokenExpiration = null;
                    context.UserDevices.Update(device);
                }

                if (expiredDevices.Any())
                {
                    context.SaveChanges();
                }
            }
        }

        public IResult RevokeAllDevicesExceptCurrent(int userId, string currentRefreshToken)
        {
            using (var context = new GymContext())
            {
                var devices = context.UserDevices.Where(d =>
                    d.UserId == userId &&
                    d.IsActive &&
                    d.RefreshToken != currentRefreshToken).ToList();

                foreach (var device in devices)
                {
                    device.IsActive = false;
                    device.RefreshToken = null;
                    device.RefreshTokenExpiration = null;
                    context.UserDevices.Update(device);
                }

                if (devices.Any())
                {
                    context.SaveChanges();
                }

                return new SuccessResult();
            }
        }

        public IDataResult<UserDevice> GetByRefreshTokenWithValidation(string refreshToken)
        {
            using (var context = new GymContext())
            {
                var device = context.UserDevices
                    .FirstOrDefault(d => d.RefreshToken == refreshToken && d.IsActive);

                if (device == null)
                    return new ErrorDataResult<UserDevice>("Geçersiz refresh token");

                // Sadece kontrol edilen token'ın süresini kontrol et
                if (device.RefreshTokenExpiration.HasValue && device.RefreshTokenExpiration.Value < DateTime.Now)
                {
                    device.IsActive = false;
                    device.RefreshToken = null;
                    device.RefreshTokenExpiration = null;
                    context.UserDevices.Update(device);
                    context.SaveChanges();
                    return new ErrorDataResult<UserDevice>("Süresi dolmuş refresh token");
                }

                return new SuccessDataResult<UserDevice>(device);
            }
        }

        /// <summary>
        /// SOLID prensiplerine uygun: Validation ve entity manipulation logic DAL katmanında
        /// </summary>
        public IResult RevokeDeviceWithValidation(int deviceId)
        {
            try
            {
                if (_context != null)
                {
                    // DI kullanılıyor - Scalability optimized
                    var device = _context.UserDevices.FirstOrDefault(d => d.Id == deviceId);
                    if (device == null)
                    {
                        return new ErrorResult("Cihaz bulunamadı");
                    }

                    device.IsActive = false;
                    device.RefreshToken = null;
                    device.RefreshTokenExpiration = null;

                    _context.SaveChanges();
                    return new SuccessResult();
                }
                else
                {
                    // Backward compatibility
                    using (var context = new GymContext())
                    {
                        var device = context.UserDevices.FirstOrDefault(d => d.Id == deviceId);
                        if (device == null)
                        {
                            return new ErrorResult("Cihaz bulunamadı");
                        }

                        device.IsActive = false;
                        device.RefreshToken = null;
                        device.RefreshTokenExpiration = null;

                        context.UserDevices.Update(device);
                        context.SaveChanges();
                        return new SuccessResult();
                    }
                }
            }
            catch (Exception ex)
            {
                return new ErrorResult($"Cihaz iptal edilirken hata oluştu: {ex.Message}");
            }
        }
    }
}
