using Core.DataAccess.EntityFramework;
using Core.Utilities.Security.CompanyContext; // ICompanyContext için eklendi
using Core.Utilities.Paging;
using DataAccess.Abstract;
using Entities.Concrete;
using Entities.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DataAccess.Concrete.EntityFramework
{
    public class EfExpenseDal : EfCompanyEntityRepositoryBase<Expense, GymContext>, IExpenseDal
    {
        private readonly ICompanyContext _companyContext;

        // Constructor injection (Scalability için)
        public EfExpenseDal(ICompanyContext companyContext, GymContext context) : base(companyContext, context)
        {
            _companyContext = companyContext;
        }

        // Backward compatibility constructor
        public EfExpenseDal(ICompanyContext companyContext) : base(companyContext)
        {
            _companyContext = companyContext;
        }

        public PaginatedResult<ExpenseDto> GetExpensesPaginated(ExpensePagingParameters parameters)
        {
            if (_context != null)
            {
                // DI kullanılıyor - Scalability optimized
                var companyId = _companyContext.GetCompanyId();
                var query = _context.Expenses
                    .Where(e => e.CompanyID == companyId && e.IsActive);

                // Filtreleme
                query = ApplyFilters(query, parameters);

                // Toplam kayıt sayısı
                var totalCount = query.Count();

                // Sıralama
                query = ApplySorting(query, parameters);

                // Sayfalama
                var expenses = query
                    .Skip((parameters.PageNumber - 1) * parameters.PageSize)
                    .Take(parameters.PageSize)
                    .Select(e => new ExpenseDto
                    {
                        ExpenseID = e.ExpenseID,
                        CompanyID = e.CompanyID,
                        Description = e.Description,
                        Amount = e.Amount,
                        ExpenseDate = e.ExpenseDate,
                        ExpenseType = e.ExpenseType,
                        CreationDate = e.CreationDate
                    })
                    .ToList();

                return new PaginatedResult<ExpenseDto>(
                    expenses,
                    parameters.PageNumber,
                    parameters.PageSize,
                    totalCount
                );
            }
            else
            {
                // Backward compatibility
                using (var context = new GymContext())
                {
                    var companyId = _companyContext.GetCompanyId();
                    var query = context.Expenses
                        .Where(e => e.CompanyID == companyId && e.IsActive);

                    // Filtreleme
                    query = ApplyFilters(query, parameters);

                    // Toplam kayıt sayısı
                    var totalCount = query.Count();

                    // Sıralama
                    query = ApplySorting(query, parameters);

                    // Sayfalama
                    var expenses = query
                        .Skip((parameters.PageNumber - 1) * parameters.PageSize)
                        .Take(parameters.PageSize)
                        .Select(e => new ExpenseDto
                        {
                            ExpenseID = e.ExpenseID,
                            CompanyID = e.CompanyID,
                            Description = e.Description,
                            Amount = e.Amount,
                            ExpenseDate = e.ExpenseDate,
                            ExpenseType = e.ExpenseType,
                            CreationDate = e.CreationDate
                        })
                        .ToList();

                    return new PaginatedResult<ExpenseDto>(
                        expenses,
                        parameters.PageNumber,
                        parameters.PageSize,
                        totalCount
                    );
                }
            }
        }

        public List<ExpenseDto> GetAllExpensesFiltered(ExpensePagingParameters parameters)
        {
            if (_context != null)
            {
                // DI kullanılıyor - Scalability optimized
                var companyId = _companyContext.GetCompanyId();
                var query = _context.Expenses
                    .Where(e => e.CompanyID == companyId && e.IsActive);

                // Filtreleme
                query = ApplyFilters(query, parameters);

                // Sıralama
                query = ApplySorting(query, parameters);

                return query
                    .Select(e => new ExpenseDto
                    {
                        ExpenseID = e.ExpenseID,
                        CompanyID = e.CompanyID,
                        Description = e.Description,
                        Amount = e.Amount,
                        ExpenseDate = e.ExpenseDate,
                        ExpenseType = e.ExpenseType,
                        CreationDate = e.CreationDate
                    })
                    .ToList();
            }
            else
            {
                // Backward compatibility
                using (var context = new GymContext())
                {
                    var companyId = _companyContext.GetCompanyId();
                    var query = context.Expenses
                        .Where(e => e.CompanyID == companyId && e.IsActive);

                    // Filtreleme
                    query = ApplyFilters(query, parameters);

                    // Sıralama
                    query = ApplySorting(query, parameters);

                    return query
                        .Select(e => new ExpenseDto
                        {
                            ExpenseID = e.ExpenseID,
                            CompanyID = e.CompanyID,
                            Description = e.Description,
                            Amount = e.Amount,
                            ExpenseDate = e.ExpenseDate,
                            ExpenseType = e.ExpenseType,
                            CreationDate = e.CreationDate
                        })
                        .ToList();
                }
            }
        }

        private IQueryable<Expense> ApplyFilters(IQueryable<Expense> query, ExpensePagingParameters parameters)
        {
            // Arama metni filtresi
            if (!string.IsNullOrEmpty(parameters.SearchText))
            {
                var searchText = parameters.SearchText.ToLower();
                query = query.Where(e =>
                    (e.Description != null && e.Description.ToLower().Contains(searchText)) ||
                    (e.ExpenseType != null && e.ExpenseType.ToLower().Contains(searchText)));
            }

            // Tarih aralığı filtresi
            if (parameters.StartDate.HasValue)
            {
                query = query.Where(e => e.ExpenseDate >= parameters.StartDate.Value);
            }

            if (parameters.EndDate.HasValue)
            {
                var endDate = parameters.EndDate.Value.Date.AddDays(1).AddTicks(-1);
                query = query.Where(e => e.ExpenseDate <= endDate);
            }

            // Gider türü filtresi
            if (!string.IsNullOrEmpty(parameters.ExpenseType))
            {
                query = query.Where(e => e.ExpenseType == parameters.ExpenseType);
            }

            // Tutar aralığı filtresi
            if (parameters.MinAmount.HasValue)
            {
                query = query.Where(e => e.Amount >= parameters.MinAmount.Value);
            }

            if (parameters.MaxAmount.HasValue)
            {
                query = query.Where(e => e.Amount <= parameters.MaxAmount.Value);
            }

            return query;
        }

        private IQueryable<Expense> ApplySorting(IQueryable<Expense> query, ExpensePagingParameters parameters)
        {
            if (string.IsNullOrEmpty(parameters.SortBy))
            {
                return query.OrderByDescending(e => e.ExpenseDate);
            }

            var isDescending = parameters.SortDirection?.ToLower() == "desc";

            return parameters.SortBy.ToLower() switch
            {
                "expensedate" => isDescending ? query.OrderByDescending(e => e.ExpenseDate) : query.OrderBy(e => e.ExpenseDate),
                "amount" => isDescending ? query.OrderByDescending(e => e.Amount) : query.OrderBy(e => e.Amount),
                "expensetype" => isDescending ? query.OrderByDescending(e => e.ExpenseType) : query.OrderBy(e => e.ExpenseType),
                "description" => isDescending ? query.OrderByDescending(e => e.Description) : query.OrderBy(e => e.Description),
                "creationdate" => isDescending ? query.OrderByDescending(e => e.CreationDate) : query.OrderBy(e => e.CreationDate),
                _ => query.OrderByDescending(e => e.ExpenseDate)
            };
        }

        public List<ExpenseDto> GetExpensesByDateRange(DateTime startDate, DateTime endDate)
        {
            using (var context = new GymContext())
            {
                var companyId = _companyContext.GetCompanyId();
                // Tarih aralığını düzelt (endDate'i gün sonuna al)
                endDate = endDate.Date.AddDays(1).AddTicks(-1);

                var result = context.Expenses
                                    .Where(e => e.CompanyID == companyId && e.IsActive && e.ExpenseDate >= startDate && e.ExpenseDate <= endDate)
                                    .Select(e => new ExpenseDto
                                    {
                                        ExpenseID = e.ExpenseID,
                                        CompanyID = e.CompanyID,
                                        Description = e.Description,
                                        Amount = e.Amount,
                                        ExpenseDate = e.ExpenseDate,
                                        ExpenseType = e.ExpenseType,
                                        CreationDate = e.CreationDate
                                    })
                                    .OrderByDescending(e => e.ExpenseDate)
                                    .ToList();
                return result;
            }
        }

        public ExpenseDashboardDto GetExpenseDashboardData(int year, int month)
        {
            using (var context = new GymContext())
            {
                var companyId = _companyContext.GetCompanyId();

                var result = new ExpenseDashboardDto
                {
                    SelectedYear = year,
                    SelectedMonth = month,
                    DataRetrievedAt = DateTime.UtcNow
                };

                // Tarih aralıklarını hesapla
                var today = DateTime.Today;
                var todayStart = today;
                var todayEnd = today.AddDays(1).AddTicks(-1);

                var monthStart = new DateTime(year, month, 1);
                var monthEnd = monthStart.AddMonths(1).AddTicks(-1);

                var yearStart = new DateTime(year, 1, 1);
                var yearEnd = new DateTime(year, 12, 31, 23, 59, 59, 999);

                // Tek sorgu ile tüm yılın verilerini al
                var yearlyExpenses = context.Expenses
                    .Where(e => e.CompanyID == companyId && e.IsActive &&
                               e.ExpenseDate >= yearStart && e.ExpenseDate <= yearEnd)
                    .Select(e => new {
                        e.ExpenseID,
                        e.Description,
                        e.Amount,
                        e.ExpenseDate,
                        e.ExpenseType,
                        e.CreationDate,
                        e.CompanyID
                    })
                    .ToList();

                // 1. Günlük toplam (bugün)
                result.TotalDailyExpense = yearlyExpenses
                    .Where(e => e.ExpenseDate >= todayStart && e.ExpenseDate <= todayEnd)
                    .Sum(e => e.Amount);

                // 2. Aylık toplam ve detaylar (seçili ay)
                var monthlyExpenses = yearlyExpenses
                    .Where(e => e.ExpenseDate >= monthStart && e.ExpenseDate <= monthEnd)
                    .ToList();

                result.TotalMonthlyExpense = monthlyExpenses.Sum(e => e.Amount);

                result.MonthlyExpenseDetails = monthlyExpenses
                    .Select(e => new ExpenseDto
                    {
                        ExpenseID = e.ExpenseID,
                        CompanyID = e.CompanyID,
                        Description = e.Description,
                        Amount = e.Amount,
                        ExpenseDate = e.ExpenseDate,
                        ExpenseType = e.ExpenseType,
                        CreationDate = e.CreationDate
                    })
                    .OrderByDescending(e => e.ExpenseDate)
                    .ToList();

                // 3. Yıllık toplam
                result.TotalYearlyExpense = yearlyExpenses.Sum(e => e.Amount);

                // 4. Aylık özet (grafik için)
                var monthlyTotals = yearlyExpenses
                    .GroupBy(e => e.ExpenseDate.Month)
                    .ToDictionary(g => g.Key, g => g.Sum(e => e.Amount));

                // 12 ayın tamamını içeren dictionary oluştur
                for (int m = 1; m <= 12; m++)
                {
                    result.MonthlyExpenseSummary[m] = monthlyTotals.ContainsKey(m) ? monthlyTotals[m] : 0m;
                }

                return result;
            }
        }

        // SOLID prensiplerine uygun: Complex business operations DAL'da
        public Core.Utilities.Results.IResult SoftDeleteExpense(int expenseId, int companyId)
        {
            try
            {
                if (_context != null)
                {
                    // DI kullanılıyor - Scalability optimized
                    var expense = _context.Expenses.FirstOrDefault(e =>
                        e.ExpenseID == expenseId && e.CompanyID == companyId);

                    if (expense == null)
                    {
                        return new Core.Utilities.Results.ErrorResult("Gider bulunamadı.");
                    }

                    // Soft delete işlemi
                    expense.IsActive = false;
                    expense.DeletedDate = DateTime.Now;
                    _context.SaveChanges();
                }
                else
                {
                    // Backward compatibility - using context olmadan
                    using (var context = new GymContext())
                    {
                        var expense = context.Expenses.FirstOrDefault(e =>
                            e.ExpenseID == expenseId && e.CompanyID == companyId);

                        if (expense == null)
                        {
                            return new Core.Utilities.Results.ErrorResult("Gider bulunamadı.");
                        }

                        // Soft delete işlemi
                        expense.IsActive = false;
                        expense.DeletedDate = DateTime.Now;
                        context.SaveChanges();
                    }
                }

                return new Core.Utilities.Results.SuccessResult("Gider başarıyla silindi.");
            }
            catch (Exception ex)
            {
                return new Core.Utilities.Results.ErrorResult($"Gider silinirken hata oluştu: {ex.Message}");
            }
        }

        // SOLID prensiplerine uygun: Add işlemi business logic ile DAL katmanında
        public Core.Utilities.Results.IResult AddExpenseWithBusinessLogic(Expense expense, int companyId)
        {
            try
            {
                // Business rule: Amount pozitif olmalı
                if (expense.Amount <= 0)
                {
                    return new Core.Utilities.Results.ErrorResult("Gider tutarı pozitif olmalıdır.");
                }

                // CompanyID ata
                expense.CompanyID = companyId;
                expense.CreationDate = DateTime.Now;
                expense.IsActive = true;

                if (_context != null)
                {
                    // DI kullanılıyor - Scalability optimized
                    _context.Expenses.Add(expense);
                    _context.SaveChanges();
                }
                else
                {
                    // Backward compatibility
                    using (var context = new GymContext())
                    {
                        context.Expenses.Add(expense);
                        context.SaveChanges();
                    }
                }

                return new Core.Utilities.Results.SuccessResult("Gider başarıyla eklendi.");
            }
            catch (Exception ex)
            {
                return new Core.Utilities.Results.ErrorResult($"Gider eklenirken hata oluştu: {ex.Message}");
            }
        }

        // SOLID prensiplerine uygun: Update işlemi business logic ile DAL katmanında
        public Core.Utilities.Results.IResult UpdateExpenseWithBusinessLogic(Expense expense, int companyId)
        {
            try
            {
                // Business rule: Amount pozitif olmalı
                if (expense.Amount <= 0)
                {
                    return new Core.Utilities.Results.ErrorResult("Gider tutarı pozitif olmalıdır.");
                }

                if (_context != null)
                {
                    // DI kullanılıyor - Scalability optimized
                    var existingExpense = _context.Expenses.FirstOrDefault(e =>
                        e.ExpenseID == expense.ExpenseID && e.CompanyID == companyId);

                    if (existingExpense == null)
                    {
                        return new Core.Utilities.Results.ErrorResult("Gider bulunamadı.");
                    }

                    // Güvenlik için CompanyID'yi tekrar ata
                    expense.CompanyID = companyId;
                    // CreationDate'in güncellenmemesini sağla
                    expense.CreationDate = existingExpense.CreationDate;
                    expense.UpdatedDate = DateTime.Now;

                    _context.Entry(existingExpense).CurrentValues.SetValues(expense);
                    _context.SaveChanges();
                }
                else
                {
                    // Backward compatibility
                    using (var context = new GymContext())
                    {
                        var existingExpense = context.Expenses.FirstOrDefault(e =>
                            e.ExpenseID == expense.ExpenseID && e.CompanyID == companyId);

                        if (existingExpense == null)
                        {
                            return new Core.Utilities.Results.ErrorResult("Gider bulunamadı.");
                        }

                        // Güvenlik için CompanyID'yi tekrar ata
                        expense.CompanyID = companyId;
                        // CreationDate'in güncellenmemesini sağla
                        expense.CreationDate = existingExpense.CreationDate;
                        expense.UpdatedDate = DateTime.Now;

                        context.Entry(existingExpense).CurrentValues.SetValues(expense);
                        context.SaveChanges();
                    }
                }

                return new Core.Utilities.Results.SuccessResult("Gider başarıyla güncellendi.");
            }
            catch (Exception ex)
            {
                return new Core.Utilities.Results.ErrorResult($"Gider güncellenirken hata oluştu: {ex.Message}");
            }
        }
    }
}