using Core.DataAccess;
using Core.Utilities.Results;
using Entities.Concrete;
using Entities.DTOs;
using System.Collections.Generic;

namespace DataAccess.Abstract
{
    public interface IWorkoutProgramTemplateDal : IEntityRepository<WorkoutProgramTemplate>
    {
        List<WorkoutProgramTemplateListDto> GetWorkoutProgramTemplateList();
        WorkoutProgramTemplateDto GetWorkoutProgramTemplateDetail(int templateId);
        bool CheckProgramNameExists(string programName, int? excludeId = null);
        void AddWorkoutProgramWithDaysAndExercises(WorkoutProgramTemplateAddDto dto, int companyId);
        void UpdateWorkoutProgramWithDaysAndExercises(WorkoutProgramTemplateUpdateDto dto, int companyId);

        // SOLID prensiplerine uygun: Validation logic DAL katmanında
        IDataResult<WorkoutProgramTemplate> GetWorkoutProgramTemplateByIdWithValidation(int templateId);
    }
}
